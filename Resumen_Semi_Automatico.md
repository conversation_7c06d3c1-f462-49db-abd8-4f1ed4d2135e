<PERSON><PERSON><PERSON>, aquí tienes un resumen completo y ordenado de la información proporcionada, listo para ser estudiado y en formato Markdown.

---

# Resumen de Seguridad de la Información y Principios de Auditoría (UBA FCE)

Este documento resume los temas clave de la materia, estructurados según el glosario provisto, y conectando los conceptos entre sí para una comprensión integral.

## 8. Awareness (Concienciación en Seguridad)

La concienciación es la primera línea de defensa, enfocada en el factor humano, que a menudo es el eslabón más débil en la cadena de seguridad.

### ¿Por qué es Fundamental?
*   El **factor humano** es el vector de ataque inicial más exitoso. La falta de conocimiento o el descuido de un usuario pueden anular defensas tecnológicas robustas.
*   Casos como **LAPSUS$** o el incidente de **Mercado Libre** demuestran que los ataques a menudo explotan la ingeniería social y el robo de credenciales, no necesariamente vulnerabilidades técnicas complejas.

### Vectores de Ataque Comunes que Explotan al Usuario
*   **Phishing:** Suplantación de identidad (generalmente por email) para engañar a la víctima y que entregue información confidencial (credenciales, datos de tarjetas).
*   **Malware:** Software malicioso (virus, troyanos, ransomware) diseñado para dañar sistemas, robar información o tomar control.
*   **Ingeniería Social:** El arte de manipular a las personas para que realicen acciones o divulguen información. Es la base de muchos ataques de phishing y malware.
*   **Robo de Identidad:** Asumir la identidad de otra persona para fines maliciosos.
*   **Fuga de Datos (Data Breach):** Exposición o robo de información confidencial.

### ¿Cómo Construir un Programa de Awareness Efectivo?
1.  **Comunicación Constante y Creativa:** No basta con comunicados ocasionales. Se deben usar campañas regulares a través de protectores de pantalla, posters, emails internos, etc.
2.  **Ejercicios Prácticos:** Realizar **simulacros de phishing** para medir la reacción de los empleados e identificar áreas de mejora. El objetivo es educar, no castigar.
3.  **Capacitación Formal:** Sesiones (online o presenciales) que expliquen los riesgos, las políticas de seguridad y cómo reportar incidentes.
4.  **Apoyo de la Dirección:** El compromiso de los líderes es crucial para que el personal se tome la seguridad en serio.
5.  **Medición de Impacto:** Evaluar métricas como la tasa de clics en simulacros de phishing o el número de incidentes reportados para justificar la inversión y dirigir esfuerzos.

### Relación con otros temas
*   **Gestión de Riesgos (ERM):** Reduce directamente el riesgo de que amenazas como el phishing se materialicen.
*   **COSO:** Fortalece el **Ambiente de Control**, uno de los componentes clave del marco.
*   **Amenazas y Ataques:** Es la defensa humana directa contra las técnicas de ingeniería social y phishing.

---

## 9. Gobierno de TI (IT Governance)

El Gobierno de TI es el marco de liderazgo y estructura organizacional que garantiza que la tecnología de la información (TI) respalde y potencie los objetivos estratégicos del negocio.

### Definición y Propósito
*   **Definición:** Conjunto de estructuras, procesos y mecanismos para dirigir y controlar los recursos de TI de forma segura y eficiente.
*   **Propósito:** Asegurar que las inversiones y el uso de la TI creen valor para los stakeholders, optimizando costos y gestionando los riesgos tecnológicos inherentes.

### Diferencia Clave: Gobierno vs. Gestión
*   **Gobierno de TI (Dirección):** Define el **qué** y el **por qué**. Establece la dirección estratégica, las políticas y los marcos de control. Es una función de supervisión ejercida por la alta dirección y el directorio.
*   **Gestión de TI (Ejecución):** Define el **cómo** y el **cuándo**. Es la función ejecutiva que planifica, construye, opera y monitorea las actividades diarias de TI, siempre bajo la dirección establecida por el gobierno.

### Pilares del Gobierno de TI
1.  **Alineamiento Estratégico:** Asegurar que la TI apoya las metas del negocio.
2.  **Roles y Responsabilidades:** Definir quién toma las decisiones de TI y quién es responsable de qué.
3.  **Gestión de Riesgos:** Identificar, evaluar y tratar los riesgos relacionados con TI.
4.  **Gestión de Recursos:** Optimizar el uso de personas, hardware, software e información.
5.  **Cumplimiento:** Garantizar el acatamiento de leyes, regulaciones y políticas internas.

### Marcos de Referencia
*   **COBIT (Control Objectives for Information and related Technology):** Es el marco más completo y destacado. Proporciona un conjunto detallado de procesos y objetivos de control para implementar un gobierno de TI efectivo.
*   **ISO/IEC 38500:** Norma internacional para el gobierno de TI.

### Relación con otros temas
*   **ITGC y Controles de Proceso:** El Gobierno de TI establece las políticas y la estructura que los ITGC y otros controles deben hacer cumplir.
*   **Gestión de Riesgos (ERM):** La gestión de riesgos de TI es un pilar fundamental del Gobierno de TI.
*   **COSO:** El Gobierno de TI es un componente esencial del gobierno corporativo general y del **Ambiente de Control**.

---

## 10 y 14. Controles Generales de TI (ITGC - IT General Controls)

Los ITGC son la base del control tecnológico. No están ligados a una aplicación específica, sino que se aplican a todo el entorno tecnológico (sistemas, redes, aplicaciones).

### ¿Por qué son tan Relevantes para la Auditoría?
*   **Son la Base:** Si los ITGC son débiles, no se puede confiar en la información generada por los sistemas ni en los controles automáticos que operan dentro de ellos.
*   **Impacto en Estados Financieros:** Afectan directamente la fiabilidad de la información financiera. Un auditor debe evaluarlos para determinar cuánta confianza puede depositar en los sistemas.
*   **Eficiencia de la Auditoría:** Si los ITGC son fuertes, el auditor puede reducir la cantidad de pruebas sustantivas (manuales), que son más costosas y lentas.

### Los 4 Dominios Principales de ITGC

#### 1. Acceso a Programas y Datos
*   **Objetivo:** Asegurar que solo las personas autorizadas tengan acceso a los sistemas y datos, y solo con los permisos necesarios para su función (**principio de mínimo privilegio**).
*   **Riesgos:** Acceso no autorizado a aplicaciones o datos, cambios no autorizados en la información.
*   **Controles Clave:**
    *   Proceso formal de alta, baja y modificación de usuarios (ABM de usuarios), con solicitudes y autorizaciones documentadas.
    *   Revisión periódica de los accesos concedidos para asegurar que sigan siendo apropiados.
    *   Controles estrictos sobre cuentas privilegiadas (administradores, superusuarios) y genéricas.
    *   Implementación de controles de **Segregación de Funciones (SoD)** para evitar conflictos de roles.

#### 2. Cambios a Programas (y Desarrollo)
*   **Objetivo:** Garantizar que solo los cambios autorizados, probados y correctos se implementen en el entorno de producción.
*   **Riesgos:** Implementar cambios con errores, no autorizados o que afecten la estabilidad y seguridad de los sistemas.
*   **Controles Clave:**
    *   **Separación de Ambientes:** Existencia de al menos tres entornos: **Desarrollo (DEV)**, **Pruebas (QA)** y **Producción (PROD)**.
    *   Proceso formal de gestión de cambios: solicitud, análisis, diseño, pruebas (incluyendo pruebas de aceptación del usuario - UAT) y aprobación formal antes del pase a producción.
    *   Segregación de funciones: El personal de desarrollo no debe tener permisos para mover cambios a producción.

#### 3. Operaciones de Cómputo
*   **Objetivo:** Asegurar que los sistemas que soportan el negocio funcionen de manera continua, correcta y segura.
*   **Riesgos:** Interrupción de los servicios, pérdida de datos, procesamiento incorrecto de transacciones.
*   **Controles Clave:**
    *   **Procesamiento de Información:** Monitoreo de procesos automáticos (jobs batch) para asegurar su correcta ejecución.
    *   **Gestión de Problemas:** Existencia de un Help Desk/Service Desk para registrar, escalar y resolver incidentes técnicos.
    *   **Backups y Recuperación:** Políticas de copias de seguridad alineadas con las necesidades del negocio (**RPO**). Proceso de restauración probado y documentado.
    *   **Plan de Recuperación ante Desastres (DRP):** Plan para recuperar la infraestructura de TI en un sitio alternativo.

### Relación con otros temas
*   **COSO:** Son la materialización de las **Actividades de Control** en el dominio de TI.
*   **BCP/DRP:** El dominio de Operaciones (backups, DRP) es el soporte técnico del BCP.
*   **Procesos de Negocio:** Unos ITGC sólidos son necesarios para poder confiar en los controles de aplicación.

---

## 11. Gestión de Riesgos (ERM - Enterprise Risk Management)

ERM es un proceso integral para identificar, evaluar y responder a los riesgos potenciales que podrían afectar el logro de los objetivos de una organización.

### Conceptos Fundamentales
*   **Riesgo:** Posibilidad de que ocurra un evento con un impacto negativo en los objetivos. Se mide por **probabilidad** e **impacto**.
*   **Marco de Referencia:** **COSO ERM** es el modelo principal. Su versión de 2017 ("Integrando con Estrategia y Desempeño") enfatiza la gestión de riesgos como una herramienta para crear y preservar valor.

### Conceptos Clave para la Toma de Decisiones
*   **Apetito de Riesgo:** La cantidad de riesgo que una organización está dispuesta a aceptar de forma estratégica para alcanzar sus objetivos.
*   **Tolerancia al Riesgo:** La desviación máxima aceptable respecto a un objetivo específico. Es un umbral de alerta.
*   **Capacidad de Riesgo:** El nivel máximo de riesgo que la organización puede soportar sin poner en peligro su existencia.

### Proceso de Gestión de Riesgos
1.  **Identificación de Riesgos:** ¿Qué puede salir mal? Se identifican riesgos en diversas categorías (estratégicos, financieros, operativos, de cumplimiento, de TI, etc.).
2.  **Evaluación de Riesgos:** Se analiza la **probabilidad** y el **impacto** de cada riesgo (mapa de calor de riesgo inherente).
3.  **Respuesta al Riesgo:** Se definen estrategias para llevar el **riesgo inherente** (riesgo bruto) a un nivel de **riesgo residual** (riesgo después de controles) aceptable. Las estrategias son:
    *   **Evitar:** Eliminar la causa del riesgo.
    *   **Reducir/Mitigar:** Implementar controles para disminuir la probabilidad o el impacto.
    *   **Transferir/Compartir:** Trasladar parte del riesgo a un tercero (ej. contratar un seguro).
    *   **Aceptar:** No tomar ninguna acción (generalmente para riesgos de bajo impacto y probabilidad).
4.  **Monitoreo:** Revisar continuamente los riesgos y la efectividad de las respuestas.

### Relación con otros temas
*   **COSO:** La evaluación de riesgos es uno de los cinco componentes del marco COSO.
*   **Controles (ITGC y de Proceso):** Son la principal herramienta para **mitigar** los riesgos identificados.
*   **BCP:** Es la respuesta específica para los riesgos de interrupción del negocio.
*   **Auditoría:** Evalúa si la organización gestiona adecuadamente sus riesgos y si los controles son efectivos.

---

## 12. COSO (Marco Integrado de Control Interno)

COSO es el marco de referencia globalmente aceptado para diseñar, implementar y evaluar la efectividad de un sistema de control interno.

### Definición y Objetivos
*   **Definición:** Un proceso, llevado a cabo por toda la organización, diseñado para proporcionar una **seguridad razonable** (no absoluta) sobre el logro de los objetivos.
*   **Tres Categorías de Objetivos:**
    1.  **Operacionales:** Eficacia y eficiencia de las operaciones.
    2.  **De Reporte (Reporting):** Fiabilidad de la información (financiera y no financiera).
    3.  **De Cumplimiento:** Adhesión a las leyes y regulaciones aplicables.

### Los 5 Componentes del Marco COSO (y sus 17 Principios)
1.  **Ambiente de Control:** Es la base de todo. Define el "tono en la cima" de la organización. Incluye la integridad, los valores éticos, la estructura organizativa y la supervisión del directorio.
2.  **Evaluación de Riesgos:** La identificación y análisis de riesgos para el logro de los objetivos. Incluye la consideración del riesgo de fraude.
3.  **Actividades de Control:** Las políticas y procedimientos que ayudan a asegurar que se ejecutan las respuestas a los riesgos (ej. autorizaciones, reconciliaciones, revisiones, ITGC, controles de aplicación, SoD).
4.  **Información y Comunicación:** La necesidad de obtener, generar y comunicar información relevante y de calidad para que el personal pueda cumplir con sus responsabilidades de control.
5.  **Actividades de Monitoreo:** Evaluaciones (continuas o puntuales) para asegurar que los componentes del control interno están presentes y funcionando. La auditoría interna es un ejemplo clave de monitoreo.

### Limitaciones Inherentes del Control Interno
*   No puede dar seguridad absoluta.
*   Está sujeto a errores humanos.
*   Puede ser eludido por la **colusión** (acuerdo entre dos o más personas).
*   La dirección puede anular los controles (**management override**).

### Relación con otros temas
*   **Es el Marco Integrador:** COSO engloba todos los demás conceptos. La **Evaluación de Riesgos** se basa en ERM. Las **Actividades de Control** incluyen los ITGC y controles de proceso/SoD. El **Gobierno de TI** es parte del Ambiente de Control. La **Auditoría** utiliza COSO como modelo para evaluar el control interno.

---

## 13. Plan de Continuidad del Negocio (BCP)

El BCP es el conjunto de planes y estrategias que permiten a una organización seguir operando sus funciones críticas durante y después de un evento disruptivo.

### Conceptos Clave
*   **BCP (Business Continuity Plan):** El plan general y proactivo que cubre la continuidad de todo el negocio (procesos, personas, tecnología).
*   **BIA (Business Impact Analysis):** El análisis previo que identifica los procesos de negocio más críticos y el impacto (financiero y operacional) de su interrupción en el tiempo.
*   **DRP (Disaster Recovery Plan):** El plan reactivo y enfocado en TI. Es parte del BCP y detalla cómo recuperar la infraestructura tecnológica y los datos tras un desastre.

### Métricas Fundamentales (Definidas en el BIA)
*   **RTO (Recovery Time Objective):** Tiempo máximo tolerable para restaurar un proceso o sistema después de una interrupción. Define **cuán rápido** debe recuperarse.
*   **RPO (Recovery Point Objective):** Período máximo tolerable de pérdida de datos. Define **a qué punto en el tiempo** se debe recuperar la información (ej. al último backup).

### Proceso
1.  **BIA:** Identificar procesos críticos y definir sus RTO y RPO.
2.  **Estrategia de Continuidad:** Definir cómo se mantendrán esos procesos (ej. sitio alternativo, trabajo remoto).
3.  **Desarrollo del Plan (BCP/DRP):** Documentar los procedimientos, roles y recursos necesarios.
4.  **Pruebas y Mantenimiento:** Probar el plan regularmente (ej. simulacros de recuperación) para asegurar que funcione y mantenerlo actualizado.

### Relación con otros temas
*   **Gestión de Riesgos (ERM):** Es la respuesta directa a los riesgos de interrupción de operaciones de alto impacto.
*   **ITGC (Operaciones):** El DRP depende críticamente de los controles de operaciones, como los backups y los procedimientos de restauración.
*   **Amenazas y Ataques:** Un ciberataque exitoso es una de las principales amenazas que pueden activar un BCP/DRP.

---

## 15. Controles en Procesos de Negocio y Segregación de Funciones (SoD)

Estos controles, también llamados "controles de aplicación", operan a nivel de transacción dentro de un proceso de negocio específico. Su objetivo es asegurar la integridad de los registros.

### Ciclos de Negocio y Controles Típicos

#### Ciclo de Compras (Purchase-to-Pay)
*   **Subprocesos:** Pedido de compra, recepción de mercadería, procesamiento de facturas, pago.
*   **Riesgos:** Pagar por bienes no recibidos, pagar a proveedores ficticios, pagos duplicados.
*   **Controles Clave:**
    *   **3-Way Match:** Comparación de la Orden de Compra, la Recepción y la Factura antes de pagar.
    *   Aprobación de órdenes de compra.
    *   Reconciliaciones bancarias.

#### Ciclo de Ventas (Order-to-Cash)
*   **Subprocesos:** Toma de pedido, envío, facturación, cobranza.
*   **Riesgos:** Facturar incorrectamente, no facturar envíos, problemas de crédito de clientes.
*   **Controles Clave:**
    *   Validación del crédito del cliente antes de aceptar el pedido.
    *   Reconciliación entre envíos y facturas.
    *   Autorización de notas de crédito.

### Segregación de Funciones (SoD - Segregation of Duties)
*   **Principio Fundamental:** Ninguna persona debe tener la capacidad de ejecutar y ocultar un error o fraude por sí sola. Se deben separar las tareas incompatibles.
*   **Beneficios:** Reduce el riesgo de error y fraude, y actúa como un elemento disuasorio.
*   **Ejemplos de Funciones Incompatibles:**
    *   Crear un proveedor y procesar pagos a ese proveedor.
    *   Registrar una venta y autorizar la nota de crédito para esa venta.
    *   Tener la custodia de un activo y acceso a los registros contables de ese activo.
*   **Controles Compensatorios:** Controles alternativos (generalmente una revisión por parte de un supervisor) que se implementan cuando la SoD completa no es factible, para mitigar el riesgo.

### Relación con otros temas
*   **COSO:** Son el ejemplo perfecto de **Actividades de Control** a nivel transaccional.
*   **ITGC:** Los controles de aplicación dependen de un entorno tecnológico fiable, garantizado por los ITGC.
*   **Auditoría:** La evaluación de los controles de proceso y la SoD es un foco central de la auditoría de procesos y financiera.

---

## 16. Normas de Auditoría

Son el marco regulatorio que asegura la calidad, independencia y consistencia del trabajo de auditoría.

### Tipos de Normas
1.  **Normas de Auditoría Externa (Locales e Internacionales):**
    *   **RT 53 (Argentina - FACPCE):** Regula la auditoría de estados financieros. Define etapas (Planificación, Ejecución, Conclusión) y principios clave.
    *   **ISA (International Standards on Auditing):** Emitidas por el IAASB. Son las normas internacionales que buscan la convergencia global. La **ISA 315** es clave, ya que exige al auditor comprender la entidad y sus riesgos, incluyendo los de TI.
2.  **Normas de Auditoría Interna:**
    *   **Normas Globales de Auditoría Interna del IIA (2024):** Emitidas por el Institute of Internal Auditors (IIA). Establecen los principios para la práctica profesional de la auditoría interna. Se estructuran en 5 dominios: Propósito, Ética y Profesionalismo, Gobernanza, Gestión y Desempeño.

### Principios Fundamentales de Auditoría
*   **Independencia:** El auditor debe ser independiente en mente y apariencia.
*   **Escepticismo Profesional:** Una actitud mental crítica y de cuestionamiento. No asumir que todo está bien sin evidencia.
*   **Evidencia de Auditoría Suficiente y Apropiada:** Base para las conclusiones del auditor.
*   **Documentación:** El trabajo del auditor debe estar debidamente documentado en papeles de trabajo.

### Tipos de Opinión (Auditoría Externa)
*   **No Modificada (Limpia):** Los estados financieros presentan razonablemente la realidad.
*   **Modificada:**
    *   **Con Salvedades:** Presentan razonablemente, *excepto por* un tema específico.
    *   **Adversa:** No presentan razonablemente la realidad.
    *   **Abstención de Opinión:** El auditor no pudo obtener evidencia para opinar.

### Relación con otros temas
*   **Son el "Reglamento":** Definen cómo el auditor debe planificar y ejecutar su trabajo (el siguiente tema) para evaluar todos los demás conceptos (COSO, ITGC, ERM, etc.).

---

## 17. Planificación, Ejecución y Pruebas de Auditoría

Describe el proceso práctico que sigue un auditor para realizar su trabajo.

### Etapas del Proceso
1.  **Planificación:**
    *   Entender la entidad, su entorno, sistemas y control interno (COSO).
    *   Evaluar los riesgos (ERM) para identificar áreas de foco.
    *   Definir el alcance, la estrategia y los procedimientos de auditoría.
2.  **Ejecución:**
    *   Realizar las pruebas planificadas para obtener evidencia.
3.  **Conclusión e Informe:**
    *   Evaluar la evidencia obtenida.
    *   Formar una opinión o conclusión.
    *   Emitir un informe con los hallazgos.

### Tipos de Pruebas de Auditoría
*   **Pruebas de Controles:** Se realizan para evaluar la **eficacia operativa** de un control. El objetivo es ver si el control funcionó como se diseñó a lo largo del período.
*   **Pruebas Sustantivas:** Se realizan para detectar errores materiales en los saldos de los estados financieros. Pueden ser:
    *   **Procedimientos Analíticos:** Análisis de tendencias y ratios.
    *   **Pruebas de Detalle:** Examen de transacciones y saldos individuales.

### El "Ciclo de Confianza" y el Walkthrough
*   Para confiar en los controles de una entidad (y así reducir las pruebas sustantivas), el auditor sigue un ciclo:
    1.  **Entender** el control.
    2.  **Evaluar su diseño** (¿es adecuado para mitigar el riesgo?).
    3.  **Probar su eficacia operativa** (¿funcionó en la práctica?).
*   El **Walkthrough (Prueba de Recorrido)** es la técnica clave para el paso 1 y 2. Consiste en seguir una única transacción de principio a fin a través del proceso para confirmar el entendimiento y evaluar el diseño de los controles.

### Documentación del Entendimiento
*   Se utilizan herramientas como **narrativas** (descripciones escritas), **diagramas de flujo** (representaciones visuales) y **matrices de riesgo y control**.

### Relación con otros temas
*   **Es la Práctica:** Es la aplicación de las **Normas de Auditoría** para evaluar el **Control Interno (COSO)**, los **ITGC** y los **Controles de Proceso**. La **Gestión de Riesgos (ERM)** guía la planificación.

---

## 18. Seguridad en el SDLC y DevSecOps

Este tema se centra en construir seguridad en el software desde su concepción, en lugar de añadirla al final.

### Conceptos Clave
*   **SDLC (Software Development Lifecycle):** El proceso estructurado para diseñar, desarrollar, probar y mantener software.
*   **Shift Left:** La práctica de mover las actividades de seguridad lo más temprano posible en el ciclo de desarrollo. Es más barato y efectivo corregir fallos en las etapas iniciales.
*   **DevSecOps:** Una cultura y un conjunto de prácticas que integran la seguridad (**Sec**) de forma automatizada en el ciclo de vida de DevOps (Desarrollo - **Dev** y Operaciones - **Ops**). La seguridad es responsabilidad de todos, no de un equipo aislado.

### Herramientas de Seguridad Automatizada en DevSecOps
*   **SAST (Static Application Security Testing):** Analiza el código fuente en busca de vulnerabilidades sin ejecutarlo.
*   **DAST (Dynamic Application Security Testing):** Prueba la aplicación mientras se está ejecutando, simulando ataques externos.
*   **SCA (Software Composition Analysis):** Analiza las librerías de terceros y componentes de código abierto utilizados en el proyecto para identificar vulnerabilidades conocidas.
*   **Fuzzing:** Envía datos inválidos, inesperados o aleatorios a una aplicación para provocar fallos.

### Modelo de Amenazas STRIDE
Es un modelo para clasificar las amenazas a la seguridad del software:
*   **S**poofing (Suplantación) -> Mitigado por **Autenticación**.
*   **T**ampering (Manipulación) -> Mitigado por **Integridad**.
*   **R**epudiation (Repudio) -> Mitigado por **No Repudio**.
*   **I**nformation Disclosure (Divulgación de Información) -> Mitigado por **Confidencialidad**.
*   **D**enial of Service (Denegación de Servicio) -> Mitigado por **Disponibilidad**.
*   **E**levation of Privilege (Elevación de Privilegios) -> Mitigado por **Autorización**.

### Relación con otros temas
*   **ITGC (Cambios):** Es la implementación de controles de seguridad dentro de este dominio.
*   **Amenazas y Ataques:** Busca construir software que sea inherentemente resistente a estas amenazas.
*   **Gestión de Riesgos (ERM):** Es una respuesta proactiva para mitigar los riesgos asociados a vulnerabilidades en el software.

---

## 19. Tipos de Amenazas y Ataques

Este tema detalla los peligros y las técnicas que los actores maliciosos utilizan para comprometer los sistemas.

### Conceptos Clave
*   **Amenaza:** Cualquier circunstancia o evento con el potencial de causar daño.
*   **Superficie de Ataque:** El conjunto total de puntos de entrada que un atacante podría usar para comprometer un sistema.
*   **IOC (Indicator of Compromise):** Evidencia forense que indica que un ataque ha ocurrido (ej. un hash de malware conocido, una IP maliciosa).
*   **IOA (Indicator of Attack):** Evidencia que indica que un ataque está en progreso (ej. un movimiento lateral en la red).

### Tipos de Malware
*   **Ransomware:** Cifra los datos de la víctima y exige un rescate para restaurarlos.
*   **Spyware:** Espía las actividades del usuario sin su conocimiento.
*   **Trojan (Troyano):** Se disfraza de software legítimo pero contiene una carga maliciosa.
*   **Worm (Gusano):** Se replica y propaga a través de redes sin intervención del usuario.

### Ataques Más Frecuentes
*   **Ransomware:** Uno de los ataques más dañinos y lucrativos.
*   **Phishing:** La puerta de entrada más común, basada en ingeniería social.
*   **Fuga de Datos (Data Breach):** El robo o exposición de información sensible.
*   **Ingeniería Social:** La manipulación psicológica de las personas. Es la base de muchos otros ataques.

### Marco de Referencia
*   **MITRE ATT&CK:** Una base de conocimiento global de tácticas y técnicas de adversarios basadas en observaciones del mundo real. Es un "mapa" de cómo operan los atacantes.

### Relación con otros temas
*   **Son el "Enemigo":** Son los eventos adversos que **ERM** busca gestionar y que los **controles (ITGC, Awareness, SDLC)** buscan prevenir.
*   **BCP/DRP:** Se activan cuando una de estas amenazas causa una interrupción mayor.
*   **Detección y Respuesta:** Se encarga de identificar y manejar los incidentes cuando una amenaza se materializa.

---

## 20. Detección y Respuesta a Incidentes de Seguridad

*Nota: Este tema no fue desarrollado en detalle en la información fuente, por lo que se presenta un resumen conceptual.*

A pesar de los controles preventivos, es inevitable que ocurran incidentes. La capacidad de una organización para detectarlos rápidamente y responder de manera efectiva es crucial.

### Proceso
1.  **Detección:** Monitorear sistemas y redes en busca de actividad sospechosa (basado en IOCs y IOAs). Esto es a menudo realizado por un **SOC (Security Operations Center)**.
2.  **Respuesta:** Un equipo especializado (**CSIRT - Computer Security Incident Response Team**) toma acción. Las fases típicas son:
    *   **Contención:** Aislar el sistema afectado para que el incidente no se propague.
    *   **Erradicación:** Eliminar la causa raíz del incidente (ej. el malware).
    *   **Recuperación:** Restaurar los sistemas a un estado seguro y operativo.
    *   **Lecciones Aprendidas:** Analizar el incidente para mejorar las defensas futuras.

### Relación con otros temas
*   **Es el Plan Reactivo:** Actúa cuando los controles preventivos fallan.
*   **Amenazas y Ataques:** Es la respuesta directa a la materialización de una amenaza.
*   **BCP/DRP:** La fase de recuperación de un incidente mayor a menudo se alinea con los procedimientos del DRP.

---

## 21. Pruebas de Penetración y Auditoría de Código

*Nota: Este tema no fue desarrollado en detalle en la información fuente, por lo que se presenta un resumen conceptual.*

Son técnicas proactivas de evaluación de seguridad para encontrar debilidades antes de que lo hagan los atacantes.

### Técnicas
*   **Pruebas de Penetración (Pentesting):**
    *   Un ataque simulado y autorizado contra un sistema para identificar vulnerabilidades explotables.
    *   Utiliza diferentes enfoques según el nivel de conocimiento del "atacante":
        *   **Black Box:** Sin conocimiento previo del sistema.
        *   **White Box:** Con conocimiento total (código fuente, arquitectura).
        *   **Gray Box:** Con conocimiento parcial (ej. credenciales de usuario estándar).
*   **Auditoría de Código (Code Review):**
    *   Una revisión manual o automatizada del código fuente de una aplicación para identificar fallos de seguridad, lógica y programación insegura. Está directamente relacionada con las herramientas **SAST**.

### Relación con otros temas
*   **SDLC y DevSecOps:** Son técnicas de verificación y validación que se integran en el ciclo de desarrollo para asegurar la calidad del software.
*   **Auditoría:** Pueden ser realizadas por auditores de seguridad o el auditor puede revisar que la organización las realice periódicamente y gestione los hallazgos.
*   **Gestión de Riesgos (ERM):** Ayudan a identificar y validar la existencia de riesgos técnicos.

---

## Conclusión General: Un Ecosistema Interconectado

Todos estos temas no son silos aislados, sino partes de un ecosistema dinámico y continuo para proteger a la organización:

*   El **Gobierno de TI** establece la dirección estratégica.
*   La **Gestión de Riesgos (ERM)** identifica lo que podría salir mal.
*   **COSO** proporciona el marco para organizar la respuesta a esos riesgos.
*   Los **Controles (ITGC, Procesos, SoD, Awareness)** son la respuesta práctica para mitigar los riesgos y prevenir que las **Amenazas** se materialicen.
*   **DevSecOps** construye defensas desde el inicio en el software.
*   El **BCP** y la **Respuesta a Incidentes** son los planes para actuar cuando los controles fallan.
*   La **Auditoría**, guiada por sus **Normas**, evalúa la eficacia de todo el sistema, proporcionando retroalimentación para la mejora continua.

Comprender la interacción entre estos elementos es fundamental para gestionar la seguridad y el control en el entorno digital actual.